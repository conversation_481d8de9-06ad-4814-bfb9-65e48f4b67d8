#!/usr/bin/env python3
"""
配置设置脚本

使用uv run来确保在正确的环境中运行
"""

import subprocess
import sys
import os


def main():
    """主函数"""
    # 检查是否在uv环境中
    if "VIRTUAL_ENV" in os.environ:
        # 在虚拟环境中，直接导入
        try:
            from dbcp_risk_report.setup_config import main as config_main
            return config_main()
        except ImportError:
            pass

    # 使用uv run来运行
    cmd = ["uv", "run", "python", "-c",
           "from dbcp_risk_report.setup_config import main; main()"]
    return subprocess.run(cmd).returncode


if __name__ == "__main__":
    sys.exit(main())
