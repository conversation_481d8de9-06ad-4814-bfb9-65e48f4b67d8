#!/usr/bin/env python3
"""
配置设置脚本

用于创建示例配置文件
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from dbcp_risk_report.config import create_sample_env_file

if __name__ == "__main__":
    create_sample_env_file()
    print("\n✅ 配置文件创建完成！")
    print("📝 请编辑 .env 文件，填入实际的配置值：")
    print("   - DBCP_API_BASE_URL: API基础URL")
    print("   - DBCP_TOKEN: 认证Token")
    print("   - DBCP_USER_ID: 用户ID")
    print("\n🚀 配置完成后，运行以下命令开始处理Excel文件：")
    print("   python run.py your_excel_file.xlsx")
