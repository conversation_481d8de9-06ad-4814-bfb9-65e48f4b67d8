"""
API客户端模块

封装所有与Web系统的API交互
"""

import requests
import logging
from typing import Dict, List, Optional, Any
from .config import Config

logger = logging.getLogger(__name__)


class APIClient:
    """API客户端类"""
    
    def __init__(self, config: Config):
        """
        初始化API客户端
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json;charset=utf-8',
            'client-type': '3',
            'Authorization': config.token
        })
        
        # 设置代理
        if config.proxies:
            self.session.proxies.update(config.proxies)
        
        # 设置SSL验证
        self.session.verify = config.verify_ssl
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数
            
        Returns:
            响应数据
            
        Raises:
            requests.RequestException: 请求失败
        """
        url = f"{self.config.api_base_url}{endpoint}"
        
        # 设置超时
        kwargs.setdefault('timeout', self.config.timeout)
        
        logger.debug(f"发送{method}请求到: {url}")
        
        for attempt in range(self.config.max_retries + 1):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                
                data = response.json()
                logger.debug(f"请求成功，响应: {data}")
                
                # 检查业务状态码
                if data.get('code') not in [200, '1']:
                    raise requests.RequestException(f"业务错误: {data.get('message', '未知错误')}")
                
                return data
                
            except requests.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {e}")
                if attempt == self.config.max_retries:
                    raise
    
    def create_report(self) -> int:
        """
        创建新报告
        
        Returns:
            报告ID
        """
        data = {
            "userId": self.config.user_id,
            "token": self.config.token
        }
        
        response = self._make_request('POST', '/report/assessment-reports/add', json=data)
        report_id = response['data']
        logger.info(f"成功创建报告，ID: {report_id}")
        return report_id
    
    def update_report_basic_info(self, report_id: int, basic_info: Dict[str, Any]) -> None:
        """
        更新报告基本信息
        
        Args:
            report_id: 报告ID
            basic_info: 基本信息数据
        """
        data = {
            "id": report_id,
            "status": "未提交",
            "userId": self.config.user_id,
            "token": self.config.token,
            **basic_info
        }
        
        self._make_request('PUT', f'/report/assessment-reports/update/{report_id}', json=data)
        logger.info(f"成功更新报告 {report_id} 的基本信息")
    
    def add_security_issue(self, report_id: int, issue_data: Dict[str, Any]) -> int:
        """
        添加高风险问题
        
        Args:
            report_id: 报告ID
            issue_data: 问题数据
            
        Returns:
            问题ID
        """
        data = {
            "assessmentReportId": report_id,
            "token": self.config.token,
            **issue_data
        }
        
        response = self._make_request('POST', '/report/security-issues/add', json=data)
        issue_id = response['data']
        logger.info(f"成功添加高风险问题，ID: {issue_id}")
        return issue_id
    
    def add_major_risk(self, report_id: int, risk_data: Dict[str, Any]) -> int:
        """
        添加重大风险隐患
        
        Args:
            report_id: 报告ID
            risk_data: 风险数据
            
        Returns:
            风险ID
        """
        data = {
            "assessmentReportId": report_id,
            "token": self.config.token,
            **risk_data
        }
        
        response = self._make_request('POST', '/report/major-risks/add', json=data)
        risk_id = response['data']
        logger.info(f"成功添加重大风险隐患，ID: {risk_id}")
        return risk_id

    # 字典数据获取方法
    def get_filing_authority_tree(self) -> List[Dict[str, Any]]:
        """获取备案机关树形数据"""
        response = self._make_request('GET', '/report/filing-authority/tree')
        return response['data']

    def get_system_level_dict(self) -> List[Dict[str, Any]]:
        """获取系统级别字典"""
        response = self._make_request('GET', '/report/sys-dict/list/system_level')
        return response['data']

    def get_location_tree(self) -> Dict[str, Any]:
        """获取测评对象所属区域树形数据"""
        response = self._make_request('GET', '/report/org/tree')
        return response['data']

    def get_industry_category_dict(self) -> List[Dict[str, Any]]:
        """获取行业类别字典"""
        response = self._make_request('GET', '/report/sys-dict/list/industry_category')
        return response['data']

    def get_extended_requirements_dict(self) -> List[Dict[str, Any]]:
        """获取扩展要求应用情况字典"""
        response = self._make_request('GET', '/report/sys-dict/list/extended_requirements')
        return response['data']

    def get_system_type_dict(self) -> List[Dict[str, Any]]:
        """获取系统类型字典"""
        response = self._make_request('GET', '/report/sys-dict/list/system_type')
        return response['data']

    def get_assessment_conclusion_dict(self) -> List[Dict[str, Any]]:
        """获取等级测评结论字典"""
        response = self._make_request('GET', '/report/sys-dict/list/assessment_conclusion')
        return response['data']

    def get_security_attribute_dict(self) -> List[Dict[str, Any]]:
        """获取安全问题属性字典"""
        response = self._make_request('GET', '/report/sys-dict/list/security_attribute')
        return response['data']

    def get_network_area_dict(self) -> List[Dict[str, Any]]:
        """获取影响的网络区域字典"""
        response = self._make_request('GET', '/report/sys-dict/list/network_area')
        return response['data']

    def get_impact_analysis_dict(self) -> List[Dict[str, Any]]:
        """获取危害分析字典"""
        response = self._make_request('GET', '/report/sys-dict/list/impact_analysis')
        return response['data']

    def get_major_risk_trigger_dict(self) -> List[Dict[str, Any]]:
        """获取重大风险隐患触发项字典"""
        response = self._make_request('GET', '/report/sys-dict/list/major_risk_trigger')
        return response['data']

    def get_rectification_status_dict(self) -> List[Dict[str, Any]]:
        """获取是否整改字典"""
        response = self._make_request('GET', '/report/sys-dict/list/rectification_status')
        return response['data']
