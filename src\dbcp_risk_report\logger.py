"""
日志工具模块

提供统一的日志配置和工具函数
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from rich.logging import RichHandler
from rich.console import Console

console = Console()


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',     # 青色
        'INFO': '\033[32m',      # 绿色
        'WARNING': '\033[33m',   # 黄色
        'ERROR': '\033[31m',     # 红色
        'CRITICAL': '\033[35m',  # 紫色
    }
    RESET = '\033[0m'
    
    def format(self, record):
        log_color = self.COLORS.get(record.levelname, self.RESET)
        record.levelname = f"{log_color}{record.levelname}{self.RESET}"
        return super().format(record)


def setup_logger(
    name: str = None,
    level: str = "INFO",
    log_file: Optional[str] = None,
    use_rich: bool = True
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        use_rich: 是否使用Rich格式化
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 设置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 控制台处理器
    if use_rich:
        console_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=True,
            markup=True
        )
        console_handler.setFormatter(logging.Formatter('%(message)s'))
    else:
        console_handler = logging.StreamHandler(sys.stdout)
        if sys.stdout.isatty():  # 如果是终端，使用彩色输出
            console_handler.setFormatter(ColoredFormatter(log_format))
        else:
            console_handler.setFormatter(logging.Formatter(log_format))
    
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logger.addHandler(file_handler)
    
    return logger


def log_function_call(func):
    """
    装饰器：记录函数调用
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数: {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def log_api_request(method: str, url: str, status_code: int = None, error: str = None):
    """
    记录API请求日志
    
    Args:
        method: HTTP方法
        url: 请求URL
        status_code: 响应状态码
        error: 错误信息
    """
    logger = logging.getLogger('api')
    
    if error:
        logger.error(f"API请求失败: {method} {url} - {error}")
    elif status_code:
        if 200 <= status_code < 300:
            logger.info(f"API请求成功: {method} {url} - {status_code}")
        else:
            logger.warning(f"API请求异常: {method} {url} - {status_code}")
    else:
        logger.debug(f"API请求: {method} {url}")


def log_data_processing(operation: str, count: int = None, details: str = None):
    """
    记录数据处理日志
    
    Args:
        operation: 操作类型
        count: 处理数量
        details: 详细信息
    """
    logger = logging.getLogger('data')
    
    message = f"数据处理: {operation}"
    if count is not None:
        message += f" (数量: {count})"
    if details:
        message += f" - {details}"
    
    logger.info(message)


def log_excel_processing(sheet_name: str, row_count: int, success: bool = True, error: str = None):
    """
    记录Excel处理日志
    
    Args:
        sheet_name: 工作表名称
        row_count: 行数
        success: 是否成功
        error: 错误信息
    """
    logger = logging.getLogger('excel')
    
    if success:
        logger.info(f"Excel工作表处理成功: {sheet_name} (行数: {row_count})")
    else:
        logger.error(f"Excel工作表处理失败: {sheet_name} - {error}")


class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, operation: str, logger_name: str = None):
        """
        初始化日志上下文
        
        Args:
            operation: 操作名称
            logger_name: 日志记录器名称
        """
        self.operation = operation
        self.logger = logging.getLogger(logger_name or __name__)
    
    def __enter__(self):
        self.logger.info(f"开始: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.logger.info(f"完成: {self.operation}")
        else:
            self.logger.error(f"失败: {self.operation} - {exc_val}")
        return False  # 不抑制异常
    
    def log_progress(self, message: str):
        """记录进度信息"""
        self.logger.info(f"{self.operation} - {message}")
    
    def log_warning(self, message: str):
        """记录警告信息"""
        self.logger.warning(f"{self.operation} - {message}")
    
    def log_error(self, message: str):
        """记录错误信息"""
        self.logger.error(f"{self.operation} - {message}")
