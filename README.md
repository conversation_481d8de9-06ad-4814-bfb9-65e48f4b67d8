# 等保报告数据录入系统

将Excel数据自动录入到Web后台系统的Python工具。

## 功能特性

- 📊 **Excel数据读取**: 支持读取包含基本信息、高风险问题、重大风险隐患的Excel文件
- 🔄 **数据转换**: 自动将Excel中的字符串数据转换为API所需的编号格式
- 🌐 **API集成**: 与Web后台系统无缝集成，自动创建报告并录入数据
- ⚙️ **灵活配置**: 支持环境变量和配置文件，可配置代理、SSL验证等
- 📝 **详细日志**: 完善的日志记录，支持文件和控制台输出
- 🎨 **美观界面**: 使用Rich库提供美观的命令行界面和进度显示

## 安装

### 环境要求

- Python 3.12+
- uv包管理器

### 安装步骤

1. 克隆项目：
```bash
git clone <repository-url>
cd dbcp_risk_report_1
```

2. 安装依赖：
```bash
uv sync
```

## 配置

### 创建配置文件

首先创建示例配置文件：

```bash
python -c "from src.dbcp_risk_report.config import create_sample_env_file; create_sample_env_file()"
```

然后复制 `.env.example` 为 `.env` 并填入实际配置：

```bash
cp .env.example .env
```

### 配置项说明

#### 必须配置项

- `DBCP_API_BASE_URL`: API基础URL，例如 `https://your-domain.com/report-backend`
- `DBCP_TOKEN`: 认证Token，格式为 `Bearer your_token_here`
- `DBCP_USER_ID`: 用户ID

#### 可选配置项

- `DBCP_HTTP_PROXY`: HTTP代理地址
- `DBCP_HTTPS_PROXY`: HTTPS代理地址
- `DBCP_VERIFY_SSL`: 是否验证SSL证书 (默认: true)
- `DBCP_TIMEOUT`: 请求超时时间，秒 (默认: 30)
- `DBCP_MAX_RETRIES`: 最大重试次数 (默认: 3)
- `DBCP_LOG_LEVEL`: 日志级别 (默认: INFO)
- `DBCP_LOG_FILE`: 日志文件路径

### 配置示例

```env
# 必须配置项
DBCP_API_BASE_URL=https://api.example.com/report-backend
DBCP_TOKEN=Bearer eyJhbGciOiJIUzI1NiJ9...
DBCP_USER_ID=1286

# 可选配置项
DBCP_HTTP_PROXY=http://proxy.company.com:8080
DBCP_VERIFY_SSL=true
DBCP_TIMEOUT=30
DBCP_LOG_LEVEL=INFO
DBCP_LOG_FILE=logs/dbcp_risk_report.log
```

## Excel文件格式

### 工作表结构

Excel文件必须包含以下三个工作表：

1. **基本信息** (第1个工作表)
   - 表头位于第3行
   - 数据位于第4行
   - 只有一条记录

2. **高风险问题** (第2个工作表)
   - 表头位于第1行
   - 数据从第2行开始
   - 可有多条记录

3. **重大风险隐患** (第3个工作表)
   - 表头位于第1行
   - 数据从第2行开始
   - 可有多条记录

### 字段说明

#### 基本信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 报告编号（自动校验编号合规） | 字符串 | 报告编号 |
| 被测单位 | 字符串 | 被测试单位名称 |
| 备案机关 | 单选 | 从系统字典选择 |
| 系统级别 | 单选 | 从系统字典选择 |
| 测评对象所属区域 | 单选 | 从系统字典选择 |
| 行业类别 | 单选 | 从系统字典选择 |
| 扩展要求应用情况 | 多选 | 用中文逗号分隔 |
| 系统类型 | 单选 | 从系统字典选择 |
| 等级测评结论 | 单选 | 从系统字典选择 |

#### 高风险问题字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 序号（自动生成） | 整数 | 问题序号 |
| 问题描述 | 字符串 | 问题详细描述 |
| 安全问题属性 | 多选 | 用中文逗号分隔 |
| 影响的网络区域（可多选） | 多选 | 用中文逗号分隔 |
| 危害分析（可多选） | 多选 | 用中文逗号分隔 |
| 是否已在重大风险隐患中填报 | 布尔 | "是"或"否" |

## 使用方法

### 快速开始

1. **创建配置文件**：
```bash
python setup_config.py
```

2. **编辑配置文件**：
复制 `.env.example` 为 `.env` 并填入实际配置值

3. **运行程序**：
```bash
python run.py <excel_file_path>
```

### 示例

```bash
# 1. 创建配置
python setup_config.py

# 2. 编辑 .env 文件（填入实际配置）

# 3. 运行程序
python run.py test.xlsx
```

### 高级用法

如果你熟悉Python模块，也可以直接使用：

```bash
python -m src.dbcp_risk_report.main <excel_file_path>
```

### 运行流程

1. 读取Excel文件
2. 初始化API客户端
3. 创建新报告
4. 转换数据格式
5. 更新报告基本信息
6. 添加高风险问题
7. 添加重大风险隐患

## 日志

系统提供详细的日志记录：

- **控制台输出**: 使用Rich库提供美观的彩色输出
- **文件日志**: 可配置日志文件路径
- **分级日志**: 支持DEBUG、INFO、WARNING、ERROR、CRITICAL级别

## 错误处理

系统包含完善的错误处理机制：

- **配置错误**: 检查必要的配置项
- **文件错误**: 验证Excel文件存在性和格式
- **网络错误**: 处理API调用失败和重试
- **数据错误**: 验证数据格式和完整性

## 开发

### 项目结构

```
src/dbcp_risk_report/
├── __init__.py          # 包初始化
├── config.py            # 配置管理
├── api_client.py        # API客户端
├── excel_reader.py      # Excel读取
├── data_converter.py    # 数据转换
├── main.py             # 主程序
├── exceptions.py        # 自定义异常
└── logger.py           # 日志工具
```

### 运行测试

```bash
uv run pytest
```

## 许可证

[MIT License](LICENSE)
