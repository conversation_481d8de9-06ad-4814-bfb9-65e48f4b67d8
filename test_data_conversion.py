#!/usr/bin/env python3
"""
数据转换测试脚本

测试数据转换功能，验证错误处理机制
"""

# 直接导入已安装的包
from dbcp_risk_report.config import load_config
from dbcp_risk_report.api_client import APIClient
from dbcp_risk_report.excel_reader import ExcelReader, BasicInfo, SecurityIssue, MajorRisk, ExcelData
from dbcp_risk_report.data_converter import DataConverter
from dbcp_risk_report.logger import setup_logger


def create_test_data():
    """创建测试数据"""
    # 创建基本信息（包含一些错误数据用于测试）
    basic_info = BasicInfo(
        report_number="45011000000-22000-24-0168-01",
        be_evaluate_unit="测试单位",
        filing_authority="不存在的备案机关",  # 这个值不存在，会导致转换失败
        system_level="第三级",
        location="南宁市",
        industry_category="证券",
        extended_requirements="大数据平台，其他",
        extended_requirements_other="容器",
        system_type="内部办公",
        assessment_conclusion="基本符合"
    )
    
    # 创建高风险问题
    security_issue = SecurityIssue(
        sequence_number=1,
        security_problem="鉴别信息以明文方式在网络环境中传输",
        security_attributes="安全技术",
        network_areas="DMZ区，其他",
        network_area_other="前置区",
        impact_analysis="数据泄露",
        impact_analysis_other=None,
        is_in_major_risk="是"
    )
    
    # 创建重大风险隐患
    major_risk = MajorRisk(
        sequence_number=1,
        security_problem="运维人员不熟悉网络架构",
        security_attributes="安全管理",
        network_areas="其他",
        network_area_other="安全管理",
        major_risk_trigger="安全管理运维水平不足",
        impact_analysis="其他",
        impact_analysis_other="人员工作失职",
        rectification_status="部分整改"
    )
    
    return ExcelData(
        basic_info=basic_info,
        security_issues=[security_issue],
        major_risks=[major_risk]
    )


def test_data_conversion():
    """测试数据转换"""
    print("数据转换测试")
    print("=" * 50)
    
    try:
        # 1. 加载配置
        print("1. 加载配置...")
        config = load_config()
        print("   ✅ 配置加载成功")
        
        # 2. 设置日志
        logger = setup_logger("test", "DEBUG")
        print("   ✅ 日志设置完成")
        
        # 3. 创建测试数据
        print("2. 创建测试数据...")
        excel_data = create_test_data()
        print("   ✅ 测试数据创建完成")
        
        # 4. 初始化API客户端（使用测试配置）
        print("3. 初始化API客户端...")
        api_client = APIClient(config)
        print("   ✅ API客户端初始化完成")
        
        # 5. 测试数据转换
        print("4. 测试数据转换...")
        converter = DataConverter(api_client)
        
        try:
            converted_data = converter.convert_excel_data(excel_data)
            print("   ✅ 数据转换成功")
            print(f"      - 基本信息转换完成")
            print(f"      - 高风险问题: {len(converted_data['security_issues'])}条")
            print(f"      - 重大风险隐患: {len(converted_data['major_risks'])}条")
            
        except ValueError as e:
            print(f"   ❌ 数据转换失败: {e}")
            print("   这是预期的行为，因为测试数据包含无效的字典值")
            
        print("\n✅ 测试完成！")
        print("💡 这个测试验证了数据转换的错误处理机制")
        print("   当Excel数据包含无效值时，系统会在创建报告前就发现并报告错误")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_data_conversion()
