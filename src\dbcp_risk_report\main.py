"""
主程序模块

整合所有模块，实现完整的数据录入流程
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.text import Text

from .config import load_config, create_sample_env_file
from .api_client import APIClient
from .excel_reader import ExcelReader
from .data_converter import DataConverter

# 设置控制台
console = Console()


def setup_logging(config):
    """设置日志"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, config.log_level),
        format=log_format,
        handlers=[]
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))
    logging.getLogger().addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if config.log_file:
        log_path = Path(config.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)


def process_excel_file(excel_file: str, config) -> bool:
    """
    处理Excel文件
    
    Args:
        excel_file: Excel文件路径
        config: 配置对象
        
    Returns:
        处理是否成功
    """
    logger = logging.getLogger(__name__)
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # 步骤1: 读取Excel文件
            task1 = progress.add_task("读取Excel文件...", total=None)
            excel_reader = ExcelReader(excel_file)
            excel_data = excel_reader.read_excel()
            progress.update(task1, description="✅ Excel文件读取完成")
            
            # 步骤2: 初始化API客户端
            task2 = progress.add_task("初始化API客户端...", total=None)
            api_client = APIClient(config)
            progress.update(task2, description="✅ API客户端初始化完成")

            # 步骤3: 转换数据格式（先验证数据，避免创建无用报告）
            task3 = progress.add_task("转换数据格式...", total=None)
            converter = DataConverter(api_client)
            converted_data = converter.convert_excel_data(excel_data)
            progress.update(task3, description="✅ 数据转换完成")

            # 步骤4: 创建报告（数据验证通过后再创建）
            task4 = progress.add_task("创建新报告...", total=None)
            report_id = api_client.create_report()
            progress.update(task4, description=f"✅ 报告创建完成 (ID: {report_id})")

            # 步骤5: 更新基本信息
            task5 = progress.add_task("更新报告基本信息...", total=None)
            api_client.update_report_basic_info(report_id, converted_data['basic_info'])
            progress.update(task5, description="✅ 基本信息更新完成")

            # 步骤6: 添加高风险问题
            if converted_data['security_issues']:
                task6 = progress.add_task(f"添加高风险问题 (共{len(converted_data['security_issues'])}条)...", total=None)
                for i, issue_data in enumerate(converted_data['security_issues']):
                    issue_id = api_client.add_security_issue(report_id, issue_data)
                    progress.update(task6, description=f"✅ 高风险问题添加完成 ({i+1}/{len(converted_data['security_issues'])})")

            # 步骤7: 添加重大风险隐患
            if converted_data['major_risks']:
                task7 = progress.add_task(f"添加重大风险隐患 (共{len(converted_data['major_risks'])}条)...", total=None)
                for i, risk_data in enumerate(converted_data['major_risks']):
                    risk_id = api_client.add_major_risk(report_id, risk_data)
                    progress.update(task7, description=f"✅ 重大风险隐患添加完成 ({i+1}/{len(converted_data['major_risks'])})")
        
        # 显示成功信息
        success_panel = Panel(
            f"[green]✅ 数据录入完成！[/green]\n\n"
            f"报告ID: {report_id}\n"
            f"基本信息: 1条\n"
            f"高风险问题: {len(converted_data['security_issues'])}条\n"
            f"重大风险隐患: {len(converted_data['major_risks'])}条",
            title="处理结果",
            border_style="green"
        )
        console.print(success_panel)
        
        return True
        
    except Exception as e:
        logger.error(f"处理失败: {e}")
        
        error_panel = Panel(
            f"[red]❌ 处理失败！[/red]\n\n"
            f"错误信息: {str(e)}\n"
            f"请检查配置和Excel文件格式",
            title="错误",
            border_style="red"
        )
        console.print(error_panel)
        
        return False


def main():
    """主函数"""
    console.print(Panel(
        "[bold blue]等保报告数据录入系统[/bold blue]\n"
        "将Excel数据自动录入到Web后台系统",
        title="DBCP Risk Report Tool",
        border_style="blue"
    ))
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        console.print("[red]用法: python -m dbcp_risk_report.main <excel_file_path>[/red]")
        console.print("\n示例: python -m dbcp_risk_report.main data.xlsx")
        return 1
    
    excel_file = sys.argv[1]
    
    # 检查Excel文件是否存在
    if not Path(excel_file).exists():
        console.print(f"[red]错误: Excel文件不存在: {excel_file}[/red]")
        return 1
    
    try:
        # 加载配置
        config = load_config()
        setup_logging(config)
        
        logger = logging.getLogger(__name__)
        logger.info(f"开始处理Excel文件: {excel_file}")
        
        # 处理Excel文件
        success = process_excel_file(excel_file, config)
        
        return 0 if success else 1
        
    except Exception as e:
        if "API基础URL必须" in str(e) or "Token不能为空" in str(e) or "用户ID不能为空" in str(e):
            console.print(f"[red]配置错误: {e}[/red]")
            console.print("\n[yellow]请检查配置文件或环境变量。如果没有配置文件，可以运行以下命令创建示例配置:[/yellow]")
            console.print("python -c \"from dbcp_risk_report.config import create_sample_env_file; create_sample_env_file()\"")
        else:
            console.print(f"[red]系统错误: {e}[/red]")
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
