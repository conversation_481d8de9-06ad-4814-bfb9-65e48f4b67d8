# 多选字段格式示例

本文档展示系统支持的多选字段格式，包括中文逗号和英文逗号的使用。

## 支持的分隔符

系统支持以下两种分隔符：
- **中文逗号**：，
- **英文逗号**：,

## 格式示例

### 1. 中文逗号分隔（原始格式）

```
扩展要求应用情况: "大数据平台，云计算平台，其他"
安全问题属性: "安全技术，安全管理"
影响的网络区域: "DMZ区，内网区，其他"
危害分析: "系统瘫痪，数据泄露，页面篡改"
```

### 2. 英文逗号分隔（新增支持）

```
扩展要求应用情况: "大数据平台,云计算平台,其他"
安全问题属性: "安全技术,安全管理"
影响的网络区域: "DMZ区,内网区,其他"
危害分析: "系统瘫痪,数据泄露,页面篡改"
```

### 3. 混合格式（灵活支持）

```
扩展要求应用情况: "大数据平台，云计算平台,其他"
安全问题属性: "安全技术,安全管理，其他"
影响的网络区域: "DMZ区，内网区,其他"
```

### 4. 带空格的格式

系统会自动去除多余的空格：

```
扩展要求应用情况: "大数据平台， 云计算平台 ,其他"
安全问题属性: "安全技术 ,安全管理， 其他"
影响的网络区域: " DMZ区 ， 内网区 , 其他 "
```

### 5. 单个值

```
扩展要求应用情况: "大数据平台"
安全问题属性: "安全技术"
```

### 6. 空值处理

```
扩展要求应用情况: ""
安全问题属性: ""
```

## 处理逻辑

### 分割过程

1. **正则表达式分割**：使用 `[，,]` 模式同时匹配中文和英文逗号
2. **去除空格**：自动去除每个值前后的空格
3. **过滤空值**：忽略空的分割结果（如连续逗号产生的空值）

### 示例代码

```python
import re

def split_multi_select(value_str):
    """分割多选值"""
    # 使用正则表达式同时支持中文逗号（，）和英文逗号（,）
    values = [v.strip() for v in re.split('[，,]', str(value_str)) if v.strip()]
    return values

# 测试示例
test_cases = [
    "大数据平台，云计算平台，其他",      # 中文逗号
    "大数据平台,云计算平台,其他",        # 英文逗号
    "大数据平台，云计算平台,其他",       # 混合逗号
    "大数据平台， 云计算平台 ,其他",     # 带空格
    "大数据平台，，云计算平台",          # 连续逗号
]

for test_str in test_cases:
    result = split_multi_select(test_str)
    print(f"输入: '{test_str}'")
    print(f"输出: {result}")
    print()
```

### 输出结果

```
输入: '大数据平台，云计算平台，其他'
输出: ['大数据平台', '云计算平台', '其他']

输入: '大数据平台,云计算平台,其他'
输出: ['大数据平台', '云计算平台', '其他']

输入: '大数据平台，云计算平台,其他'
输出: ['大数据平台', '云计算平台', '其他']

输入: '大数据平台， 云计算平台 ,其他'
输出: ['大数据平台', '云计算平台', '其他']

输入: '大数据平台，，云计算平台'
输出: ['大数据平台', '云计算平台']
```

## 实际应用场景

### Excel文件中的多选字段

在Excel文件中，用户可以使用任何一种格式：

| 字段名 | 值 | 解析结果 |
|--------|-----|----------|
| 扩展要求应用情况 | 大数据平台，云计算平台 | ["ER006", "ER001"] |
| 扩展要求应用情况 | 大数据平台,云计算平台 | ["ER006", "ER001"] |
| 安全问题属性 | 安全技术，安全管理 | ["SA001", "SA002"] |
| 安全问题属性 | 安全技术,安全管理 | ["SA001", "SA002"] |

### 错误处理

如果多选值中包含无效选项：

```
输入: "大数据平台，不存在的选项，云计算平台"
处理: 
- 有效值: ["大数据平台", "云计算平台"] -> ["ER006", "ER001"]
- 无效值: ["不存在的选项"] -> 记录警告
- 结果: 继续处理，使用有效值
```

## 兼容性优势

1. **向后兼容**：完全支持原有的中文逗号格式
2. **用户友好**：支持用户习惯的英文逗号输入
3. **容错性强**：自动处理空格和连续分隔符
4. **灵活性高**：支持混合格式，适应不同用户习惯

## 最佳实践

1. **推荐格式**：建议在Excel模板中说明支持两种逗号
2. **用户培训**：告知用户可以使用任何一种逗号格式
3. **数据检查**：在数据录入前可以预览分割结果
4. **错误提示**：当值无效时，系统会提供详细的错误信息
