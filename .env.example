# 等保报告数据录入系统配置文件
# 复制此文件为 .env 并填入实际配置值

# 必须配置项
DBCP_API_BASE_URL=https://your-api-domain.com/report-backend
DBCP_TOKEN=Bearer your_token_here
DBCP_USER_ID=your_user_id_here

# 可选配置项
# DBCP_HTTP_PROXY=http://proxy.company.com:8080
# DBCP_HTTPS_PROXY=https://proxy.company.com:8080
# DBCP_VERIFY_SSL=true
# DBCP_TIMEOUT=30
# DBCP_MAX_RETRIES=3

# 日志配置
# DBCP_LOG_LEVEL=INFO
# DBCP_LOG_FILE=logs/dbcp_risk_report.log
