# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 测试
.coverage
.pytest_cache/
htmlcov/

# 配置文件（包含敏感信息）
config_local.py
.env

# Excel测试文件
*.xlsx
*.xls
test_data/

# 临时文件
*.tmp
*.temp