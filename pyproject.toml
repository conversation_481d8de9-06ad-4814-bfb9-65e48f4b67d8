[project]
name = "dbcp-risk-report-1"
version = "0.1.0"
description = "等保报告数据录入系统 - 将Excel数据自动录入到Web后台系统"
requires-python = ">=3.12"
dependencies = [
    "openpyxl>=3.1.0",
    "requests>=2.31.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "rich>=13.7.0",
]

[project.scripts]
dbcp-risk-report = "dbcp_risk_report.main:main"
dbcp-setup-config = "dbcp_risk_report.setup_config:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "isort>=5.12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/dbcp_risk_report"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88


