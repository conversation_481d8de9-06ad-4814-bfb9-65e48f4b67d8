"""
配置管理模块

管理应用程序的配置项，包括API地址、认证信息等
"""

import os
from typing import Optional
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Config(BaseModel):
    """应用程序配置类"""
    
    # 必须配置项
    api_base_url: str = Field(..., description="API基础URL")
    token: str = Field(..., description="认证Token")
    user_id: str = Field(..., description="用户ID")
    
    # 可选配置项
    http_proxy: Optional[str] = Field(None, description="HTTP代理地址")
    https_proxy: Optional[str] = Field(None, description="HTTPS代理地址")
    verify_ssl: bool = Field(True, description="是否验证SSL证书")
    timeout: int = Field(30, description="请求超时时间（秒）")
    max_retries: int = Field(3, description="最大重试次数")
    
    # 日志配置
    log_level: str = Field("INFO", description="日志级别")
    log_file: Optional[str] = Field(None, description="日志文件路径")
    
    @validator('api_base_url')
    def validate_api_base_url(cls, v):
        """验证API基础URL格式"""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API基础URL必须以http://或https://开头')
        return v.rstrip('/')
    
    @validator('token')
    def validate_token(cls, v):
        """验证Token格式"""
        if not v.strip():
            raise ValueError('Token不能为空')
        return v.strip()
    
    @validator('user_id')
    def validate_user_id(cls, v):
        """验证用户ID格式"""
        if not v.strip():
            raise ValueError('用户ID不能为空')
        return v.strip()
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是以下之一: {", ".join(valid_levels)}')
        return v.upper()
    
    @property
    def proxies(self) -> Optional[dict]:
        """获取代理配置"""
        if self.http_proxy or self.https_proxy:
            return {
                'http': self.http_proxy,
                'https': self.https_proxy or self.http_proxy
            }
        return None
    
    class Config:
        env_prefix = "DBCP_"  # 环境变量前缀


def load_config() -> Config:
    """
    加载配置
    
    优先级：环境变量 > .env文件 > 默认值
    """
    return Config(
        api_base_url=os.getenv('DBCP_API_BASE_URL', ''),
        token=os.getenv('DBCP_TOKEN', ''),
        user_id=os.getenv('DBCP_USER_ID', ''),
        http_proxy=os.getenv('DBCP_HTTP_PROXY'),
        https_proxy=os.getenv('DBCP_HTTPS_PROXY'),
        verify_ssl=os.getenv('DBCP_VERIFY_SSL', 'true').lower() == 'true',
        timeout=int(os.getenv('DBCP_TIMEOUT', '30')),
        max_retries=int(os.getenv('DBCP_MAX_RETRIES', '3')),
        log_level=os.getenv('DBCP_LOG_LEVEL', 'INFO'),
        log_file=os.getenv('DBCP_LOG_FILE')
    )


def create_sample_env_file(file_path: str = '.env.example'):
    """创建示例环境变量文件"""
    sample_content = """# 等保报告数据录入系统配置文件
# 复制此文件为 .env 并填入实际配置值

# 必须配置项
DBCP_API_BASE_URL=https://your-api-domain.com/report-backend
DBCP_TOKEN=Bearer your_token_here
DBCP_USER_ID=your_user_id_here

# 可选配置项
# DBCP_HTTP_PROXY=http://proxy.company.com:8080
# DBCP_HTTPS_PROXY=https://proxy.company.com:8080
# DBCP_VERIFY_SSL=true
# DBCP_TIMEOUT=30
# DBCP_MAX_RETRIES=3

# 日志配置
# DBCP_LOG_LEVEL=INFO
# DBCP_LOG_FILE=logs/dbcp_risk_report.log
"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print(f"示例配置文件已创建: {file_path}")
    print("请复制为 .env 文件并填入实际配置值")


if __name__ == "__main__":
    # 创建示例配置文件
    create_sample_env_file()
    
    # 尝试加载配置（用于测试）
    try:
        config = load_config()
        print("配置加载成功:")
        print(f"  API基础URL: {config.api_base_url}")
        print(f"  用户ID: {config.user_id}")
        print(f"  日志级别: {config.log_level}")
    except Exception as e:
        print(f"配置加载失败: {e}")
