#!/usr/bin/env python3
"""
多选值分割测试

测试中文逗号和英文逗号的支持
"""

# 不再需要sys.path操作，直接导入已安装的包

import re


def test_multi_select_split():
    """测试多选值分割功能"""
    print("多选值分割测试")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        "大数据平台，云计算平台，其他",  # 中文逗号
        "大数据平台,云计算平台,其他",    # 英文逗号
        "大数据平台，云计算平台,其他",   # 混合逗号
        "大数据平台， 云计算平台 ,其他", # 带空格
        "大数据平台",                   # 单个值
        "",                            # 空值
        "大数据平台，，云计算平台",      # 连续逗号
        "大数据平台,,云计算平台",       # 连续英文逗号
    ]
    
    for i, test_str in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: '{test_str}'")
        
        # 使用正则表达式分割
        values = [v.strip() for v in re.split('[，,]', str(test_str)) if v.strip()]
        
        print(f"  分割结果: {values}")
        print(f"  值的数量: {len(values)}")
    
    print("\n✅ 测试完成！")
    print("💡 系统现在支持中文逗号（，）和英文逗号（,）两种分隔符")


def test_real_data_conversion():
    """测试真实数据转换"""
    print("\n" + "=" * 50)
    print("真实数据转换测试")
    print("=" * 50)
    
    try:
        from dbcp_risk_report.config import load_config
        from dbcp_risk_report.api_client import APIClient
        from dbcp_risk_report.data_converter import DataConverter
        
        # 加载配置
        config = load_config()
        api_client = APIClient(config)
        converter = DataConverter(api_client)
        
        # 测试不同的多选值格式
        test_values = [
            "安全技术，安全管理",      # 中文逗号
            "安全技术,安全管理",       # 英文逗号
            "安全技术， 安全管理",     # 中文逗号 + 空格
            "安全技术, 安全管理",      # 英文逗号 + 空格
            "安全技术，安全管理,其他", # 混合逗号
        ]
        
        for i, test_value in enumerate(test_values, 1):
            print(f"\n测试 {i}: '{test_value}'")
            try:
                # 测试安全问题属性转换
                result = converter._convert_multi_select('security_attribute', test_value, required=False)
                print(f"  转换结果: {result}")
                print(f"  ✅ 转换成功")
            except Exception as e:
                print(f"  ❌ 转换失败: {e}")
        
        print("\n✅ 真实数据转换测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("💡 这可能是因为API连接问题，但分割功能本身是正常的")


if __name__ == "__main__":
    test_multi_select_split()
    test_real_data_conversion()
