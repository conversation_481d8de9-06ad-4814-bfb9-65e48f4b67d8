"""
数据转换模块

负责将Excel中的字符串数据转换为API所需的编号格式
"""

import logging
from typing import Dict, List, Any, Optional, Union
from .api_client import APIClient
from .excel_reader import ExcelData, BasicInfo, SecurityIssue, MajorRisk

logger = logging.getLogger(__name__)


class DataConverter:
    """数据转换器"""
    
    def __init__(self, api_client: APIClient):
        """
        初始化数据转换器
        
        Args:
            api_client: API客户端
        """
        self.api_client = api_client
        self._dict_cache = {}  # 字典数据缓存
        
        logger.info("初始化数据转换器")
    
    def _get_dict_data(self, dict_type: str) -> List[Dict[str, Any]]:
        """
        获取字典数据（带缓存）
        
        Args:
            dict_type: 字典类型
            
        Returns:
            字典数据列表
        """
        if dict_type not in self._dict_cache:
            logger.debug(f"获取字典数据: {dict_type}")
            
            if dict_type == 'filing_authority':
                self._dict_cache[dict_type] = self._flatten_filing_authority_tree()
            elif dict_type == 'location':
                self._dict_cache[dict_type] = self._flatten_location_tree()
            else:
                # 通用字典数据获取
                method_map = {
                    'system_level': self.api_client.get_system_level_dict,
                    'industry_category': self.api_client.get_industry_category_dict,
                    'extended_requirements': self.api_client.get_extended_requirements_dict,
                    'system_type': self.api_client.get_system_type_dict,
                    'assessment_conclusion': self.api_client.get_assessment_conclusion_dict,
                    'security_attribute': self.api_client.get_security_attribute_dict,
                    'network_area': self.api_client.get_network_area_dict,
                    'impact_analysis': self.api_client.get_impact_analysis_dict,
                    'major_risk_trigger': self.api_client.get_major_risk_trigger_dict,
                    'rectification_status': self.api_client.get_rectification_status_dict,
                }
                
                if dict_type in method_map:
                    self._dict_cache[dict_type] = method_map[dict_type]()
                else:
                    raise ValueError(f"未知的字典类型: {dict_type}")
        
        return self._dict_cache[dict_type]
    
    def _flatten_filing_authority_tree(self) -> List[Dict[str, Any]]:
        """扁平化备案机关树形数据"""
        tree_data = self.api_client.get_filing_authority_tree()
        flattened = []
        
        def flatten_node(node):
            flattened.append({
                'dictValue': node['orgCode'],
                'dictDesc': node['orgName']
            })
            if node.get('children'):
                for child in node['children']:
                    flatten_node(child)
        
        for root in tree_data:
            flatten_node(root)
        
        return flattened
    
    def _flatten_location_tree(self) -> List[Dict[str, Any]]:
        """扁平化区域树形数据"""
        tree_data = self.api_client.get_location_tree()
        flattened = []
        
        def flatten_org_list(org_list):
            for org in org_list:
                flattened.append({
                    'dictValue': org['districtCode'],
                    'dictDesc': org['orgName']
                })
                if org.get('orgList'):
                    flatten_org_list(org['orgList'])
        
        if tree_data.get('orgList'):
            flatten_org_list(tree_data['orgList'])
        
        return flattened
    
    def _find_dict_value(self, dict_type: str, desc: str, required: bool = True) -> Optional[str]:
        """
        根据描述查找字典值

        Args:
            dict_type: 字典类型
            desc: 描述文本
            required: 是否必须找到匹配项

        Returns:
            字典值编码，未找到时根据required参数决定是否抛出异常

        Raises:
            ValueError: 当required=True且未找到匹配项时抛出异常
        """
        if not desc or not desc.strip():
            if required:
                raise ValueError(f"字典类型 {dict_type} 的描述值为空")
            return None

        dict_data = self._get_dict_data(dict_type)
        desc = desc.strip()

        for item in dict_data:
            if item['dictDesc'] == desc:
                return item['dictValue']

        # 未找到匹配项
        available_options = [item['dictDesc'] for item in dict_data[:10]]  # 只显示前10个选项
        error_msg = f"未找到匹配的字典值: {dict_type} - '{desc}'"
        if available_options:
            error_msg += f"\n可用选项包括: {', '.join(available_options)}"
            if len(dict_data) > 10:
                error_msg += f" 等（共{len(dict_data)}个选项）"

        if required:
            raise ValueError(error_msg)

        logger.warning(error_msg)
        return None
    
    def _convert_multi_select(self, dict_type: str, value_str: str, required: bool = True) -> List[str]:
        """
        转换多选字符串为编码列表

        Args:
            dict_type: 字典类型
            value_str: 多选值字符串（支持中文逗号，或英文逗号,分隔）
            required: 是否要求至少有一个有效值

        Returns:
            编码列表

        Raises:
            ValueError: 当required=True且没有找到任何有效值时抛出异常
        """
        if not value_str or not str(value_str).strip():
            if required:
                raise ValueError(f"字典类型 {dict_type} 的多选值为空")
            return []

        # 按中文逗号或英文逗号分割
        import re
        # 使用正则表达式同时支持中文逗号（，）和英文逗号（,）
        values = [v.strip() for v in re.split('[，,]', str(value_str)) if v.strip()]
        if not values:
            if required:
                raise ValueError(f"字典类型 {dict_type} 的多选值解析后为空")
            return []

        codes = []
        failed_values = []

        for value in values:
            try:
                code = self._find_dict_value(dict_type, value, required=True)
                codes.append(code)
            except ValueError:
                failed_values.append(value)

        # 如果有转换失败的值，记录警告或抛出异常
        if failed_values:
            error_msg = f"字典类型 {dict_type} 中以下值转换失败: {', '.join(failed_values)}"
            if required and not codes:
                raise ValueError(f"{error_msg}，且没有任何有效值")
            if failed_values:
                logger.warning(error_msg)

        if required and not codes:
            raise ValueError(f"字典类型 {dict_type} 没有找到任何有效的编码值")

        return codes
    
    def _convert_boolean(self, value: str) -> int:
        """
        转换布尔值
        
        Args:
            value: 字符串值
            
        Returns:
            1表示是，0表示否
        """
        if str(value).strip() == '是':
            return 1
        if str(value).strip() == '否':
            return 0

        logger.warning(f"未知的布尔值: {value}")
        return 0

    def convert_basic_info(self, basic_info: BasicInfo) -> Dict[str, Any]:
        """
        转换基本信息

        Args:
            basic_info: 基本信息对象

        Returns:
            转换后的API数据
        """
        logger.debug("转换基本信息")

        # 转换扩展要求应用情况
        extended_requirements_codes = self._convert_multi_select(
            'extended_requirements', basic_info.extended_requirements
        )

        data = {
            "reportNumber": basic_info.report_number,
            "beEvaluateUnit": basic_info.be_evaluate_unit,
            "filingAuthority": self._find_dict_value('filing_authority', basic_info.filing_authority, required=True),
            "systemLevelCode": self._find_dict_value('system_level', basic_info.system_level, required=True),
            "locationCode": self._find_dict_value('location', basic_info.location, required=True),
            "industryCategoryCode": self._find_dict_value('industry_category', basic_info.industry_category, required=True),
            "extendedRequirementsApplicationCodes": extended_requirements_codes,
            "systemTypeCode": self._find_dict_value('system_type', basic_info.system_type, required=True),
            "assessmentConclusionCode": self._find_dict_value('assessment_conclusion', basic_info.assessment_conclusion, required=True),
        }

        # 如果扩展要求包含"其他"，添加其他内容
        if basic_info.extended_requirements_other:
            data["extendedRequirementsApplicationOther"] = basic_info.extended_requirements_other

        return data

    def convert_security_issue(self, issue: SecurityIssue) -> Dict[str, Any]:
        """
        转换高风险问题

        Args:
            issue: 高风险问题对象

        Returns:
            转换后的API数据
        """
        logger.debug(f"转换高风险问题: {issue.sequence_number}")

        data = {
            "securityProblem": issue.security_problem,
            "securityAttributeCodes": self._convert_multi_select('security_attribute', issue.security_attributes),
            "networkAreaCodes": self._convert_multi_select('network_area', issue.network_areas),
            "impactAnalysisCodes": self._convert_multi_select('impact_analysis', issue.impact_analysis),
            "isInMajorRisk": self._convert_boolean(issue.is_in_major_risk),
        }

        # 如果网络区域包含"其他"，添加其他内容
        if issue.network_area_other:
            data["networkAreaOther"] = issue.network_area_other

        # 如果危害分析包含"其他"，添加其他内容
        if issue.impact_analysis_other:
            data["impactAnalysisOther"] = issue.impact_analysis_other

        return data

    def convert_major_risk(self, risk: MajorRisk) -> Dict[str, Any]:
        """
        转换重大风险隐患

        Args:
            risk: 重大风险隐患对象

        Returns:
            转换后的API数据
        """
        logger.debug(f"转换重大风险隐患: {risk.sequence_number}")

        data = {
            "securityProblem": risk.security_problem,
            "securityAttributeCodes": self._convert_multi_select('security_attribute', risk.security_attributes),
            "networkAreaCodes": self._convert_multi_select('network_area', risk.network_areas),
            "majorRiskTriggerCode": self._find_dict_value('major_risk_trigger', risk.major_risk_trigger, required=True),
            "impactAnalysisCodes": self._convert_multi_select('impact_analysis', risk.impact_analysis),
            "rectificationStatusCode": self._find_dict_value('rectification_status', risk.rectification_status, required=True),
        }

        # 如果网络区域包含"其他"，添加其他内容
        if risk.network_area_other:
            data["networkAreaOther"] = risk.network_area_other

        # 如果危害分析包含"其他"，添加其他内容
        if risk.impact_analysis_other:
            data["impactAnalysisOther"] = risk.impact_analysis_other

        return data

    def convert_excel_data(self, excel_data: ExcelData) -> Dict[str, Any]:
        """
        转换完整的Excel数据

        Args:
            excel_data: Excel数据对象

        Returns:
            转换后的完整数据

        Raises:
            ValueError: 数据转换失败时抛出异常
        """
        logger.info("开始转换Excel数据")

        try:
            # 转换基本信息
            logger.debug("转换基本信息...")
            basic_info = self.convert_basic_info(excel_data.basic_info)
            self._validate_converted_basic_info(basic_info)

            # 转换高风险问题
            logger.debug(f"转换高风险问题 ({len(excel_data.security_issues)}条)...")
            security_issues = []
            for i, issue in enumerate(excel_data.security_issues):
                try:
                    converted_issue = self.convert_security_issue(issue)
                    self._validate_converted_security_issue(converted_issue, i + 1)
                    security_issues.append(converted_issue)
                except Exception as e:
                    raise ValueError(f"高风险问题第{i+1}条转换失败: {e}")

            # 转换重大风险隐患
            logger.debug(f"转换重大风险隐患 ({len(excel_data.major_risks)}条)...")
            major_risks = []
            for i, risk in enumerate(excel_data.major_risks):
                try:
                    converted_risk = self.convert_major_risk(risk)
                    self._validate_converted_major_risk(converted_risk, i + 1)
                    major_risks.append(converted_risk)
                except Exception as e:
                    raise ValueError(f"重大风险隐患第{i+1}条转换失败: {e}")

            logger.info("Excel数据转换完成")
            return {
                'basic_info': basic_info,
                'security_issues': security_issues,
                'major_risks': major_risks
            }

        except Exception as e:
            logger.error(f"Excel数据转换失败: {e}")
            raise

    def _validate_converted_basic_info(self, basic_info: Dict[str, Any]) -> None:
        """验证转换后的基本信息"""
        required_fields = [
            'reportNumber', 'beEvaluateUnit', 'filingAuthority',
            'systemLevelCode', 'locationCode', 'industryCategoryCode',
            'systemTypeCode', 'assessmentConclusionCode'
        ]

        for field in required_fields:
            if not basic_info.get(field):
                raise ValueError(f"基本信息字段 {field} 转换失败或为空")

    def _validate_converted_security_issue(self, issue: Dict[str, Any], index: int) -> None:
        """验证转换后的高风险问题"""
        if not issue.get('securityProblem'):
            raise ValueError(f"第{index}条高风险问题的问题描述为空")

        if not issue.get('securityAttributeCodes'):
            raise ValueError(f"第{index}条高风险问题的安全问题属性转换失败")

    def _validate_converted_major_risk(self, risk: Dict[str, Any], index: int) -> None:
        """验证转换后的重大风险隐患"""
        if not risk.get('securityProblem'):
            raise ValueError(f"第{index}条重大风险隐患的问题描述为空")

        if not risk.get('securityAttributeCodes'):
            raise ValueError(f"第{index}条重大风险隐患的安全问题属性转换失败")

        if not risk.get('majorRiskTriggerCode'):
            raise ValueError(f"第{index}条重大风险隐患的触发项转换失败")
