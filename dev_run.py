#!/usr/bin/env python3
"""
开发环境运行脚本

专门用于开发和调试，使用PYTHONPATH方式导入模块
"""

import sys
import os
from pathlib import Path

# 添加src目录到PYTHONPATH，仅用于开发调试
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# 设置环境变量，让子模块也能找到包
os.environ["PYTHONPATH"] = str(src_path) + os.pathsep + os.environ.get("PYTHONPATH", "")

# 导入并运行主程序
from dbcp_risk_report.main import main


if __name__ == "__main__":
    print("🔧 开发模式运行")
    print(f"📁 使用源码路径: {src_path}")
    print("-" * 50)
    sys.exit(main())
