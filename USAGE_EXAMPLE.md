# 使用示例

本文档展示等保报告数据录入系统的完整使用流程，包括成功和失败的场景。

## 场景1：数据验证成功

### 1. 准备工作

```bash
# 安装依赖
uv sync

# 创建配置
python setup_config.py

# 编辑 .env 文件，填入实际配置
```

### 2. 运行程序

```bash
python run.py test.xlsx
```

### 3. 成功输出示例

```
等保报告数据录入系统
将Excel数据自动录入到Web后台系统

✅ Excel文件读取完成
✅ API客户端初始化完成
✅ 数据转换完成
✅ 报告创建完成 (ID: 922)
✅ 基本信息更新完成
✅ 高风险问题添加完成 (2/2)
✅ 重大风险隐患添加完成 (2/2)

┌─────────────────────────────────────────┐
│                处理结果                 │
├─────────────────────────────────────────┤
│ ✅ 数据录入完成！                       │
│                                         │
│ 报告ID: 922                             │
│ 基本信息: 1条                           │
│ 高风险问题: 2条                         │
│ 重大风险隐患: 2条                       │
└─────────────────────────────────────────┘
```

## 场景2：数据验证失败

### 1. Excel数据包含错误

假设Excel文件中的"备案机关"字段填写了不存在的值："不存在的机关"

### 2. 运行程序

```bash
python run.py test_with_errors.xlsx
```

### 3. 失败输出示例

```
等保报告数据录入系统
将Excel数据自动录入到Web后台系统

✅ Excel文件读取完成
✅ API客户端初始化完成
❌ 数据转换失败

┌─────────────────────────────────────────┐
│                  错误                   │
├─────────────────────────────────────────┤
│ ❌ 处理失败！                           │
│                                         │
│ 错误信息: 未找到匹配的字典值:           │
│ filing_authority - '不存在的机关'       │
│                                         │
│ 可用选项包括:                           │
│ 上海市公安局, 黄埔区公安局,             │
│ 徐汇区公安局, 长宁区公安局,             │
│ 静安区公安局 等（共539个选项）          │
│                                         │
│ 请检查配置和Excel文件格式               │
└─────────────────────────────────────────┘
```

### 4. 关键优势

- **没有创建无用报告**：系统在发现数据错误后，不会在Web系统中创建报告
- **详细错误信息**：明确指出哪个字段有问题
- **修复建议**：提供可用的选项，帮助用户快速修复数据

## 场景3：部分数据转换失败

### 1. 多选字段部分值无效

假设"扩展要求应用情况"字段值为："大数据平台，不存在的要求，云计算平台"

### 2. 系统行为

```
⚠️  字典类型 extended_requirements 中以下值转换失败: 不存在的要求
✅ 数据转换完成（部分值被忽略）
✅ 报告创建完成 (ID: 923)
```

系统会：
- 记录警告信息
- 使用有效的值继续处理
- 成功创建报告

## 场景4：网络连接问题

### 1. API服务不可用

```bash
python run.py test.xlsx
```

### 2. 输出示例

```
✅ Excel文件读取完成
❌ API客户端连接失败

┌─────────────────────────────────────────┐
│                  错误                   │
├─────────────────────────────────────────┤
│ ❌ 处理失败！                           │
│                                         │
│ 错误信息: 连接超时                      │
│ 请检查网络连接和API配置                 │
└─────────────────────────────────────────┘
```

## 最佳实践

### 1. 数据准备

- **使用标准模板**：确保Excel文件格式符合要求
- **验证字典值**：使用系统中存在的字典值
- **检查必填字段**：确保所有必填字段都有值

### 2. 配置管理

- **使用环境变量**：敏感信息通过环境变量管理
- **测试连接**：运行前先测试API连接
- **备份配置**：保存配置文件的备份

### 3. 错误处理

- **仔细阅读错误信息**：系统提供详细的错误描述
- **逐步修复**：一次修复一个错误，然后重新运行
- **保存日志**：启用日志文件记录，便于问题追踪

### 4. 性能优化

- **批量处理**：一次处理多个Excel文件时，重用API客户端
- **网络配置**：在企业环境中正确配置代理
- **日志级别**：生产环境使用INFO级别，调试时使用DEBUG

## 常见问题

### Q: 如何知道系统支持哪些字典值？

A: 运行时如果遇到无效值，系统会显示可用选项。也可以查看API文档或联系系统管理员。

### Q: 数据转换失败后如何修复？

A: 
1. 查看错误信息中的具体字段
2. 参考提供的可用选项
3. 修改Excel文件中的对应值
4. 重新运行程序

### Q: 如何处理"其他"类型的字段？

A: 
1. 在多选字段中包含"其他"选项
2. 在对应的"其他内容"字段中填写具体描述
3. 系统会自动处理这种关联关系

### Q: 程序运行很慢怎么办？

A: 
1. 检查网络连接速度
2. 确认API服务响应正常
3. 考虑调整超时和重试配置
4. 查看日志了解具体耗时环节
