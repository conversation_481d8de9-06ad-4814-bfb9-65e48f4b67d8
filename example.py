#!/usr/bin/env python3
"""
使用示例

演示如何使用等保报告数据录入系统
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from dbcp_risk_report.config import load_config, create_sample_env_file
from dbcp_risk_report.api_client import APIClient
from dbcp_risk_report.excel_reader import ExcelReader
from dbcp_risk_report.data_converter import DataConverter
from dbcp_risk_report.logger import setup_logger


def main():
    """主函数"""
    print("等保报告数据录入系统 - 使用示例")
    print("=" * 50)
    
    # 1. 创建示例配置文件（如果不存在）
    env_file = Path(".env")
    if not env_file.exists():
        print("1. 创建示例配置文件...")
        create_sample_env_file()
        print("   ✅ 已创建 .env.example 文件")
        print("   ⚠️  请复制为 .env 文件并填入实际配置值")
        return
    
    try:
        # 2. 加载配置
        print("2. 加载配置...")
        config = load_config()
        print("   ✅ 配置加载成功")
        
        # 3. 设置日志
        print("3. 设置日志...")
        logger = setup_logger("example", config.log_level, config.log_file)
        print("   ✅ 日志设置完成")
        
        # 4. 检查Excel文件
        excel_file = "test.xlsx"
        if not Path(excel_file).exists():
            print(f"   ❌ Excel文件不存在: {excel_file}")
            print("   请确保Excel文件存在并符合格式要求")
            return
        
        print(f"4. 检查Excel文件: {excel_file}")
        print("   ✅ Excel文件存在")
        
        # 5. 读取Excel数据
        print("5. 读取Excel数据...")
        excel_reader = ExcelReader(excel_file)
        excel_data = excel_reader.read_excel()
        print(f"   ✅ 读取完成:")
        print(f"      - 基本信息: 1条")
        print(f"      - 高风险问题: {len(excel_data.security_issues)}条")
        print(f"      - 重大风险隐患: {len(excel_data.major_risks)}条")
        
        # 6. 初始化API客户端
        print("6. 初始化API客户端...")
        api_client = APIClient(config)
        print("   ✅ API客户端初始化完成")
        
        # 7. 测试API连接（获取字典数据）
        print("7. 测试API连接...")
        try:
            system_levels = api_client.get_system_level_dict()
            print(f"   ✅ API连接正常，获取到 {len(system_levels)} 个系统级别选项")
        except Exception as e:
            print(f"   ❌ API连接失败: {e}")
            return
        
        # 8. 转换数据
        print("8. 转换数据...")
        converter = DataConverter(api_client)
        converted_data = converter.convert_excel_data(excel_data)
        print("   ✅ 数据转换完成")
        
        # 9. 显示转换结果示例
        print("9. 转换结果示例:")
        basic_info = converted_data['basic_info']
        print(f"   报告编号: {basic_info.get('reportNumber')}")
        print(f"   被测单位: {basic_info.get('beEvaluateUnit')}")
        print(f"   系统级别代码: {basic_info.get('systemLevelCode')}")
        
        print("\n✅ 示例运行完成！")
        print("💡 要执行完整的数据录入，请运行:")
        print(f"   python -m src.dbcp_risk_report.main {excel_file}")
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        print("请检查配置和Excel文件格式")


if __name__ == "__main__":
    main()
