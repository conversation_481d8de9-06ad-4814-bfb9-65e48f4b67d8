# 快速开始指南

## 1. 安装依赖

确保已安装uv包管理器，然后运行：

```bash
uv sync
```

## 2. 配置系统

### 创建配置文件

```bash
python setup_config.py
```

### 编辑配置

复制 `.env.example` 为 `.env` 并填入实际配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入以下必须配置项：

```env
DBCP_API_BASE_URL=https://your-api-domain.com/report-backend
DBCP_TOKEN=Bearer your_token_here
DBCP_USER_ID=your_user_id_here
```

## 3. 准备Excel文件

确保Excel文件包含三个工作表：
1. **基本信息** - 表头在第3行，数据在第4行
2. **高风险问题** - 表头在第1行，数据从第2行开始
3. **重大风险隐患** - 表头在第1行，数据从第2行开始

## 4. 运行程序

```bash
python run.py your_excel_file.xlsx
```

## 5. 查看结果

程序会显示处理进度和最终结果，包括：
- 创建的报告ID
- 处理的数据条数
- 任何错误或警告信息

## 故障排除

### 配置错误
- 检查 `.env` 文件中的API地址、Token和用户ID
- 确保API地址以 `http://` 或 `https://` 开头

### Excel格式错误
- 确保工作表数量和名称正确
- 检查表头位置是否符合要求
- 验证数据格式是否正确

### 网络错误
- 检查网络连接
- 如果使用代理，确保代理配置正确
- 检查SSL证书验证设置

### API错误
- 验证Token是否有效
- 检查用户权限
- 查看详细错误日志

## 示例运行

```bash
# 1. 创建配置
python setup_config.py

# 2. 编辑 .env 文件（填入实际配置）

# 3. 使用示例Excel文件
python run.py test.xlsx

# 查看详细日志（在 .env 文件中设置 DBCP_LOG_LEVEL=DEBUG）

# 使用代理（在 .env 文件中设置代理配置）
```
