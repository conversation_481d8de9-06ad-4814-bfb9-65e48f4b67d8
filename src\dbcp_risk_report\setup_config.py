#!/usr/bin/env python3
"""
配置设置模块

用于创建示例配置文件
"""

from .config import create_sample_env_file


def main():
    """主函数"""
    create_sample_env_file()
    print("\n✅ 配置文件创建完成！")
    print("📝 请编辑 .env 文件，填入实际的配置值：")
    print("   - DBCP_API_BASE_URL: API基础URL")
    print("   - DBCP_TOKEN: 认证Token")
    print("   - DBCP_USER_ID: 用户ID")
    print("\n🚀 配置完成后，运行以下命令开始处理Excel文件：")
    print("   dbcp-risk-report your_excel_file.xlsx")
    print("   或者: python -m dbcp_risk_report.main your_excel_file.xlsx")


if __name__ == "__main__":
    main()
