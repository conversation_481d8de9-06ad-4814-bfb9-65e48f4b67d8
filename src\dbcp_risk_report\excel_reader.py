"""
Excel数据读取模块

负责读取Excel文件的三个工作表，解析数据并进行基本验证
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from openpyxl import load_workbook
from pydantic import BaseModel, validator

logger = logging.getLogger(__name__)


class BasicInfo(BaseModel):
    """基本信息数据模型"""
    report_number: str  # 报告编号
    be_evaluate_unit: str  # 被测单位
    filing_authority: str  # 备案机关
    system_level: str  # 系统级别
    location: str  # 测评对象所属区域
    industry_category: str  # 行业类别
    extended_requirements: str  # 扩展要求应用情况
    extended_requirements_other: Optional[str] = None  # 扩展要求其他内容
    system_type: str  # 系统类型
    assessment_conclusion: str  # 等级测评结论
    
    @validator('report_number')
    def validate_report_number(cls, v):
        """验证报告编号格式"""
        if not v or not v.strip():
            raise ValueError('报告编号不能为空')
        return v.strip()
    
    @validator('be_evaluate_unit')
    def validate_be_evaluate_unit(cls, v):
        """验证被测单位"""
        if not v or not v.strip():
            raise ValueError('被测单位不能为空')
        return v.strip()


class SecurityIssue(BaseModel):
    """高风险问题数据模型"""
    sequence_number: int  # 序号
    security_problem: str  # 问题描述
    security_attributes: str  # 安全问题属性
    network_areas: str  # 影响的网络区域
    network_area_other: Optional[str] = None  # 网络区域其他内容
    impact_analysis: str  # 危害分析
    impact_analysis_other: Optional[str] = None  # 危害分析其他内容
    is_in_major_risk: str  # 是否已在重大风险隐患中填报
    
    @validator('security_problem')
    def validate_security_problem(cls, v):
        """验证问题描述"""
        if not v or not v.strip():
            raise ValueError('问题描述不能为空')
        return v.strip()


class MajorRisk(BaseModel):
    """重大风险隐患数据模型"""
    sequence_number: int  # 序号
    security_problem: str  # 问题描述
    security_attributes: str  # 安全问题属性
    network_areas: str  # 影响的网络区域
    network_area_other: Optional[str] = None  # 网络区域其他内容
    major_risk_trigger: str  # 重大风险隐患触发项
    impact_analysis: str  # 危害分析
    impact_analysis_other: Optional[str] = None  # 危害分析其他内容
    rectification_status: str  # 是否整改
    
    @validator('security_problem')
    def validate_security_problem(cls, v):
        """验证问题描述"""
        if not v or not v.strip():
            raise ValueError('问题描述不能为空')
        return v.strip()


class ExcelData(BaseModel):
    """Excel数据容器"""
    basic_info: BasicInfo
    security_issues: List[SecurityIssue]
    major_risks: List[MajorRisk]


class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化Excel读取器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = Path(file_path)
        if not self.file_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {file_path}")
        
        logger.info(f"初始化Excel读取器: {file_path}")
    
    def read_excel(self) -> ExcelData:
        """
        读取Excel文件
        
        Returns:
            解析后的Excel数据
        """
        logger.info("开始读取Excel文件")
        
        try:
            workbook = load_workbook(self.file_path, data_only=True)
            
            # 读取三个工作表
            basic_info = self._read_basic_info(workbook)
            security_issues = self._read_security_issues(workbook)
            major_risks = self._read_major_risks(workbook)
            
            logger.info(f"Excel读取完成: 基本信息1条, 高风险问题{len(security_issues)}条, 重大风险隐患{len(major_risks)}条")
            
            return ExcelData(
                basic_info=basic_info,
                security_issues=security_issues,
                major_risks=major_risks
            )
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def _read_basic_info(self, workbook) -> BasicInfo:
        """读取基本信息工作表"""
        logger.debug("读取基本信息工作表")
        
        # 获取第一个工作表
        sheet = workbook.worksheets[0]
        
        # 表头在第3行，数据在第4行
        headers = [cell.value for cell in sheet[3]]
        data_row = [cell.value for cell in sheet[4]]
        
        # 创建数据字典
        data_dict = dict(zip(headers, data_row))
        
        # 处理扩展要求应用情况的"其他"内容
        extended_requirements_other = None
        extended_requirements = data_dict.get('扩展要求应用情况', '')
        if '其他' in extended_requirements:
            extended_requirements_other = data_dict.get('【扩展要求应用情况】勾选"其他"后必须其他内容')
        
        return BasicInfo(
            report_number=data_dict.get('报告编号（自动校验编号合规）', ''),
            be_evaluate_unit=data_dict.get('被测单位', ''),
            filing_authority=data_dict.get('备案机关', ''),
            system_level=data_dict.get('系统级别', ''),
            location=data_dict.get('测评对象所属区域', ''),
            industry_category=data_dict.get('行业类别', ''),
            extended_requirements=extended_requirements,
            extended_requirements_other=extended_requirements_other,
            system_type=data_dict.get('系统类型', ''),
            assessment_conclusion=data_dict.get('等级测评结论', '')
        )

    def _read_security_issues(self, workbook) -> List[SecurityIssue]:
        """读取高风险问题工作表"""
        logger.debug("读取高风险问题工作表")

        # 获取第二个工作表
        sheet = workbook.worksheets[1]

        # 表头在第1行，数据从第2行开始
        headers = [cell.value for cell in sheet[1]]

        issues = []
        for row_num in range(2, sheet.max_row + 1):
            row_data = [cell.value for cell in sheet[row_num]]

            # 跳过空行
            if not any(row_data):
                continue

            data_dict = dict(zip(headers, row_data))

            # 处理网络区域的"其他"内容
            network_area_other = None
            network_areas = data_dict.get('"影响的网络区域（可多选）"', '')
            if '其他' in str(network_areas):
                network_area_other = data_dict.get('【影响的网络区域】勾选"其他"后必须填写其他内容')

            # 处理危害分析的"其他"内容
            impact_analysis_other = None
            impact_analysis = data_dict.get('"危害分析（可多选）"', '')
            if '其他' in str(impact_analysis):
                impact_analysis_other = data_dict.get('【危害分析】勾选"其他"后必须填写其他内容')

            # 处理布尔值
            is_in_major_risk = data_dict.get('是否已在重大风险隐患中填报', '')

            issue = SecurityIssue(
                sequence_number=int(data_dict.get('"序号（自动生成）"', 0) or 0),
                security_problem=data_dict.get('问题描述', ''),
                security_attributes=data_dict.get('安全问题属性', ''),
                network_areas=network_areas,
                network_area_other=network_area_other,
                impact_analysis=impact_analysis,
                impact_analysis_other=impact_analysis_other,
                is_in_major_risk=str(is_in_major_risk)
            )

            issues.append(issue)

        return issues

    def _read_major_risks(self, workbook) -> List[MajorRisk]:
        """读取重大风险隐患工作表"""
        logger.debug("读取重大风险隐患工作表")

        # 获取第三个工作表
        sheet = workbook.worksheets[2]

        # 表头在第1行，数据从第2行开始
        headers = [cell.value for cell in sheet[1]]

        risks = []
        for row_num in range(2, sheet.max_row + 1):
            row_data = [cell.value for cell in sheet[row_num]]

            # 跳过空行
            if not any(row_data):
                continue

            data_dict = dict(zip(headers, row_data))

            # 处理网络区域的"其他"内容
            network_area_other = None
            network_areas = data_dict.get('"影响的网络区域（可多选）"', '')
            if '其他' in str(network_areas):
                network_area_other = data_dict.get('【影响的网络区域】勾选"其他"后必须填写其他内容')

            # 处理危害分析的"其他"内容
            impact_analysis_other = None
            impact_analysis = data_dict.get('"危害分析（可多选）"', '')
            if '其他' in str(impact_analysis):
                impact_analysis_other = data_dict.get('【危害分析】勾选"其他"后必须填写其他内容')

            risk = MajorRisk(
                sequence_number=int(data_dict.get('"序号（自动生成）"', 0) or 0),
                security_problem=data_dict.get('问题描述', ''),
                security_attributes=data_dict.get('安全问题属性', ''),
                network_areas=network_areas,
                network_area_other=network_area_other,
                major_risk_trigger=data_dict.get('重大风险隐患触发项', ''),
                impact_analysis=impact_analysis,
                impact_analysis_other=impact_analysis_other,
                rectification_status=data_dict.get('是否整改', '')
            )

            risks.append(risk)

        return risks
