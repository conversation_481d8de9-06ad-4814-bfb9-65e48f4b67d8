"""
配置模块测试
"""

import pytest
import os
from unittest.mock import patch
from src.dbcp_risk_report.config import Config, load_config


class TestConfig:
    """配置类测试"""
    
    def test_valid_config(self):
        """测试有效配置"""
        config = Config(
            api_base_url="https://api.example.com/report-backend",
            token="Bearer test_token",
            user_id="1286"
        )
        
        assert config.api_base_url == "https://api.example.com/report-backend"
        assert config.token == "Bearer test_token"
        assert config.user_id == "1286"
        assert config.verify_ssl is True
        assert config.timeout == 30
    
    def test_api_base_url_validation(self):
        """测试API基础URL验证"""
        # 无效URL
        with pytest.raises(ValueError, match="API基础URL必须以http://或https://开头"):
            Config(
                api_base_url="invalid-url",
                token="Bearer test_token",
                user_id="1286"
            )
        
        # 有效URL会自动去除尾部斜杠
        config = Config(
            api_base_url="https://api.example.com/report-backend/",
            token="Bearer test_token",
            user_id="1286"
        )
        assert config.api_base_url == "https://api.example.com/report-backend"
    
    def test_token_validation(self):
        """测试Token验证"""
        # 空Token
        with pytest.raises(ValueError, match="Token不能为空"):
            Config(
                api_base_url="https://api.example.com",
                token="",
                user_id="1286"
            )
        
        # 空白Token
        with pytest.raises(ValueError, match="Token不能为空"):
            Config(
                api_base_url="https://api.example.com",
                token="   ",
                user_id="1286"
            )
    
    def test_user_id_validation(self):
        """测试用户ID验证"""
        # 空用户ID
        with pytest.raises(ValueError, match="用户ID不能为空"):
            Config(
                api_base_url="https://api.example.com",
                token="Bearer test_token",
                user_id=""
            )
    
    def test_log_level_validation(self):
        """测试日志级别验证"""
        # 无效日志级别
        with pytest.raises(ValueError, match="日志级别必须是以下之一"):
            Config(
                api_base_url="https://api.example.com",
                token="Bearer test_token",
                user_id="1286",
                log_level="INVALID"
            )
        
        # 有效日志级别（小写会自动转换为大写）
        config = Config(
            api_base_url="https://api.example.com",
            token="Bearer test_token",
            user_id="1286",
            log_level="debug"
        )
        assert config.log_level == "DEBUG"
    
    def test_proxies_property(self):
        """测试代理配置属性"""
        # 无代理
        config = Config(
            api_base_url="https://api.example.com",
            token="Bearer test_token",
            user_id="1286"
        )
        assert config.proxies is None
        
        # 只有HTTP代理
        config = Config(
            api_base_url="https://api.example.com",
            token="Bearer test_token",
            user_id="1286",
            http_proxy="http://proxy.example.com:8080"
        )
        expected_proxies = {
            'http': 'http://proxy.example.com:8080',
            'https': 'http://proxy.example.com:8080'
        }
        assert config.proxies == expected_proxies
        
        # HTTP和HTTPS代理都有
        config = Config(
            api_base_url="https://api.example.com",
            token="Bearer test_token",
            user_id="1286",
            http_proxy="http://proxy.example.com:8080",
            https_proxy="https://proxy.example.com:8443"
        )
        expected_proxies = {
            'http': 'http://proxy.example.com:8080',
            'https': 'https://proxy.example.com:8443'
        }
        assert config.proxies == expected_proxies


class TestLoadConfig:
    """配置加载测试"""
    
    @patch.dict(os.environ, {
        'DBCP_API_BASE_URL': 'https://test.example.com/api',
        'DBCP_TOKEN': 'Bearer test_env_token',
        'DBCP_USER_ID': 'test_user',
        'DBCP_VERIFY_SSL': 'false',
        'DBCP_TIMEOUT': '60',
        'DBCP_LOG_LEVEL': 'DEBUG'
    })
    def test_load_config_from_env(self):
        """测试从环境变量加载配置"""
        config = load_config()
        
        assert config.api_base_url == 'https://test.example.com/api'
        assert config.token == 'Bearer test_env_token'
        assert config.user_id == 'test_user'
        assert config.verify_ssl is False
        assert config.timeout == 60
        assert config.log_level == 'DEBUG'
    
    @patch.dict(os.environ, {}, clear=True)
    def test_load_config_missing_required(self):
        """测试缺少必需配置项"""
        with pytest.raises(ValueError):
            load_config()
